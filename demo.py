#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK反编译工程演示脚本
展示主要功能
"""

import json
import sys
from pathlib import Path

# 添加scripts目录到路径
sys.path.append(str(Path(__file__).parent / "scripts"))

from utils import print_banner, print_separator, get_file_size_human

def show_project_structure():
    """显示项目结构"""
    print_banner()
    print("📁 项目结构展示")
    print_separator()
    
    structure = {
        "📄 配置文件": ["README.md", "requirements.txt", "setup.py", "LICENSE"],
        "📁 核心脚本": ["scripts/decompile.py", "scripts/analyze_apk.py", "scripts/download_tools.py"],
        "📁 工具目录": ["tools/apktool.jar", "tools/jadx/", "tools/dex2jar/"],
        "📁 输入输出": ["apk/", "output/", "config/"],
        "📁 文档": ["docs/usage.md", "docs/installation.md"]
    }
    
    for category, files in structure.items():
        print(f"\n{category}:")
        for file in files:
            status = "✓" if Path(file).exists() else "✗"
            print(f"  {status} {file}")

def show_apk_analysis_summary():
    """显示APK分析摘要"""
    print_separator()
    print("📊 APK分析结果摘要")
    print_separator()
    
    analysis_file = Path("output/analysis/com.legend.startknife.tap_75_analysis.json")
    
    if not analysis_file.exists():
        print("❌ 分析文件不存在，请先运行: python scripts/analyze_apk.py")
        return
    
    with open(analysis_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    apk_info = data["apk_info"]
    dex_info = data["dex_files"]
    lib_info = data["native_libraries"]
    structure = data["file_structure"]
    
    print(f"🎮 应用名称: {apk_info['file_name']}")
    print(f"📦 文件大小: {get_file_size_human(apk_info['file_size'])}")
    print(f"🔒 MD5哈希: {apk_info['md5']}")
    print(f"📱 支持架构: {', '.join(lib_info['architectures'])}")
    print(f"📂 文件总数: {structure['total_files']:,}")
    print(f"💾 DEX大小: {get_file_size_human(dex_info['total_dex_size'])}")
    
    print(f"\n🔧 原生库数量:")
    for arch, libs in lib_info["libraries"].items():
        print(f"  {arch}: {len(libs)} 个库")
    
    print(f"\n📄 主要文件类型:")
    top_types = sorted(structure["file_types"].items(), key=lambda x: x[1], reverse=True)[:10]
    for ext, count in top_types:
        print(f"  .{ext}: {count:,} 个")

def show_tools_status():
    """显示工具状态"""
    print_separator()
    print("🛠️ 反编译工具状态")
    print_separator()
    
    tools = {
        "apktool": "tools/apktool.jar",
        "jadx": "tools/jadx/bin/jadx.bat",
        "dex2jar": "tools/dex2jar/d2j-dex2jar.bat"
    }
    
    for tool, path in tools.items():
        status = "✅ 已安装" if Path(path).exists() else "❌ 未安装"
        size = ""
        if Path(path).exists() and Path(path).is_file():
            size = f" ({get_file_size_human(Path(path).stat().st_size)})"
        print(f"  {tool}: {status}{size}")

def show_usage_examples():
    """显示使用示例"""
    print_separator()
    print("💡 使用示例")
    print_separator()
    
    examples = [
        ("环境检查", "python check_env.py"),
        ("下载工具", "python scripts/download_tools.py"),
        ("分析APK", "python scripts/analyze_apk.py"),
        ("反编译APK", "python scripts/decompile.py"),
        ("分析特定APK", "python scripts/analyze_apk.py --apk path/to/app.apk"),
        ("反编译特定APK", "python scripts/decompile.py --apk path/to/app.apk")
    ]
    
    for desc, cmd in examples:
        print(f"📌 {desc}:")
        print(f"   {cmd}")
        print()

def main():
    """主函数"""
    print("🚀 APK反编译工程演示")
    
    show_project_structure()
    show_tools_status()
    show_apk_analysis_summary()
    show_usage_examples()
    
    print_separator("=")
    print("✨ 演示完成！这个APK反编译工程已经完全可用。")
    print("📖 详细使用说明请查看 docs/usage.md")

if __name__ == "__main__":
    main()
