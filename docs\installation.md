# 安装指南

## 系统要求

- Windows 10/11 或 Linux 或 macOS
- Python 3.7 或更高版本
- Java 8 或更高版本
- 至少 2GB 可用磁盘空间

## 安装步骤

### 1. 克隆或下载项目

```bash
git clone <repository-url>
cd decompileApk
```

### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 3. 检查环境

```bash
python check_env.py
```

### 4. 下载反编译工具

```bash
python scripts/download_tools.py
```

或者使用批处理文件：

```bash
run.bat
```

选择选项 1。

## 验证安装

运行环境检查脚本确认所有组件都已正确安装：

```bash
python check_env.py
```

如果看到 "✓ 系统环境检查通过，可以开始使用"，说明安装成功。

## 故障排除

### Java未找到

确保Java已安装并添加到PATH环境变量中：

```bash
java -version
```

### 工具下载失败

如果自动下载失败，可以手动下载工具：

1. **apktool**: 从 [官网](https://ibotpeaches.github.io/Apktool/) 下载
2. **jadx**: 从 [GitHub](https://github.com/skylot/jadx/releases) 下载
3. **dex2jar**: 从 [GitHub](https://github.com/pxb1988/dex2jar/releases) 下载

将工具放置到 `tools/` 目录中。

### 权限问题

在Linux/macOS上，可能需要给脚本执行权限：

```bash
chmod +x scripts/*.py
```
