#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
cipherTxt逆向分析工具包
提供多种分析方法来理解加密实现
"""

import base64
import json
import hashlib
import hmac
from pathlib import Path
import subprocess
import re

class CipherReverseToolkit:
    def __init__(self):
        self.package_name = "com.legend.startknife.tap"
        self.output_dir = Path("output/cipher_analysis")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def xor_decrypt(self, encrypted_str, key=16):
        """XOR解密函数 (基于发现的算法)"""
        try:
            result = ""
            for char in encrypted_str:
                result += chr(ord(char) ^ key)
            return result
        except:
            return encrypted_str
    
    def analyze_encrypted_strings(self):
        """分析加密字符串"""
        print("🔍 分析加密字符串")
        print("=" * 40)
        
        # 从反编译代码中提取的加密字符串
        encrypted_strings = [
            "q~tb\u007fyt>q``>QsdyfydiDxbuqt",
            "sebbu~dQsdyfydiDxbuqt", 
            "wudCicdu}S\u007f~duhd",
            "sebbu~dQsdyfydiDxbuqt",
            "wud@b\u007fsucc^q}u",
            "q~tb\u007fyt>s\u007f~du~d>`}>@qs{qwu@qbcub4@qs{qwu",
            "q~tb\u007fyt>q``>QsdyfydiDxbuqt",
            "}Xyttu~Q`yGqb~y~wCx\u007fg~",
            "s\u007f}>zw>rx>Bu`\u007fbdcDy}u",
            "BuwycdubQsdyfydiSq||Rqs{c"
        ]
        
        print("解密结果:")
        for enc_str in encrypted_strings:
            dec_str = self.xor_decrypt(enc_str)
            print(f"  '{enc_str}' → '{dec_str}'")
        
        return encrypted_strings
    
    def generate_cipher_test_cases(self):
        """生成cipherTxt测试用例"""
        print("\n🧪 生成cipherTxt测试用例")
        print("=" * 40)
        
        # 模拟游戏数据
        test_data = {
            "action": "battle_settlement",
            "player_id": "12345",
            "level": 10,
            "score": 9999,
            "timestamp": 1640995200,
            "items": ["sword", "shield", "potion"]
        }
        
        json_data = json.dumps(test_data, separators=(',', ':'))
        print(f"原始数据: {json_data}")
        
        # 测试不同的加密方案
        test_cases = []
        
        # 1. 简单Base64
        base64_result = base64.b64encode(json_data.encode()).decode()
        test_cases.append(("Base64", base64_result))
        
        # 2. XOR + Base64
        xor_data = ""
        for char in json_data:
            xor_data += chr(ord(char) ^ 16)
        xor_base64 = base64.b64encode(xor_data.encode('latin1')).decode()
        test_cases.append(("XOR16+Base64", xor_base64))
        
        # 3. 简单哈希
        hash_result = hashlib.sha256(json_data.encode()).hexdigest()
        test_cases.append(("SHA256", hash_result))
        
        # 4. HMAC签名
        hmac_result = hmac.new(b"secret_key", json_data.encode(), hashlib.sha256).hexdigest()
        test_cases.append(("HMAC-SHA256", hmac_result))
        
        print("\n测试用例:")
        for name, result in test_cases:
            print(f"  {name}: {result[:64]}...")
        
        # 保存测试用例
        with open(self.output_dir / "test_cases.json", "w") as f:
            json.dump({
                "original_data": test_data,
                "json_string": json_data,
                "test_cases": dict(test_cases)
            }, f, indent=2)
        
        return test_cases
    
    def analyze_themis_patterns(self):
        """分析Themis加密模式"""
        print("\n🔐 分析Themis加密模式")
        print("=" * 40)
        
        # Themis库的典型输出格式
        themis_patterns = {
            "Secure Cell (Seal)": {
                "description": "AES-256-GCM + 认证标签",
                "format": "IV(12) + EncryptedData + AuthTag(16)",
                "base64_length": "变长，取决于数据大小"
            },
            "Secure Cell (Token Protect)": {
                "description": "AES-256-GCM，分离认证标签",
                "format": "EncryptedData + 分离的AuthToken",
                "base64_length": "数据长度 + 固定Token长度"
            },
            "Secure Message": {
                "description": "RSA + AES混合加密",
                "format": "Header + EncryptedKey + EncryptedData",
                "base64_length": "较长，包含RSA加密的密钥"
            }
        }
        
        print("Themis可能的加密模式:")
        for mode, info in themis_patterns.items():
            print(f"\n  {mode}:")
            print(f"    描述: {info['description']}")
            print(f"    格式: {info['format']}")
            print(f"    长度: {info['base64_length']}")
        
        return themis_patterns
    
    def create_frida_runner(self):
        """创建Frida运行脚本"""
        print("\n🔧 创建Frida运行脚本")
        print("=" * 40)
        
        frida_script = f"""#!/bin/bash
# Frida动态分析脚本

echo "开始Frida动态分析..."

# 检查设备连接
adb devices

# 启动应用
adb shell am start -n {self.package_name}/.MainActivity

# 等待应用启动
sleep 3

# 运行Frida脚本
frida -U -f {self.package_name} -l scripts/frida_cipher_hook.js --no-pause

echo "Frida分析完成"
"""
        
        script_path = self.output_dir / "run_frida.sh"
        with open(script_path, "w") as f:
            f.write(frida_script)
        
        # Windows版本
        windows_script = f"""@echo off
echo 开始Frida动态分析...

REM 检查设备连接
adb devices

REM 启动应用
adb shell am start -n {self.package_name}/.MainActivity

REM 等待应用启动
timeout /t 3

REM 运行Frida脚本
frida -U -f {self.package_name} -l scripts/frida_cipher_hook.js --no-pause

echo Frida分析完成
pause
"""
        
        windows_path = self.output_dir / "run_frida.bat"
        with open(windows_path, "w") as f:
            f.write(windows_script)
        
        print(f"Frida脚本已创建:")
        print(f"  Linux/Mac: {script_path}")
        print(f"  Windows: {windows_path}")
        
        return script_path, windows_path
    
    def generate_analysis_report(self):
        """生成分析报告"""
        print("\n📊 生成分析报告")
        print("=" * 40)
        
        report = {
            "analysis_summary": {
                "target_app": self.package_name,
                "protection": "360加固 (jiagu)",
                "encryption_lib": "Themis (Cossack Labs)",
                "string_obfuscation": "XOR 16",
                "architecture": "混合实现 (JavaScript + Native)"
            },
            "key_findings": {
                "string_encryption": "XOR 16用于字符串混淆",
                "code_protection": "360加固保护核心代码",
                "crypto_library": "libthemis.so提供AES/RSA加密",
                "network_layer": "可能在原生库或加密DEX中实现"
            },
            "cipher_generation_hypothesis": {
                "most_likely": "混合实现",
                "flow": [
                    "JavaScript层准备游戏数据",
                    "JSON序列化数据",
                    "JNI调用原生加密函数",
                    "Themis库执行AES-256-GCM加密",
                    "Base64编码输出cipherTxt",
                    "原生HTTP客户端发送请求"
                ],
                "encryption_algorithm": "AES-256-GCM",
                "key_management": "RSA密钥交换或预共享密钥",
                "integrity_protection": "HMAC-SHA256或GCM认证标签"
            },
            "reverse_engineering_strategy": {
                "dynamic_analysis": [
                    "使用Frida Hook JNI调用",
                    "监控libthemis.so加密函数",
                    "拦截网络请求和响应",
                    "追踪Base64编码过程"
                ],
                "static_analysis": [
                    "分析libthemis.so二进制文件",
                    "反编译JavaScript .jsc文件",
                    "查找AES/RSA相关函数",
                    "提取密钥生成逻辑"
                ],
                "tools_needed": [
                    "Frida (动态分析)",
                    "IDA Pro/Ghidra (静态分析)",
                    "Cocos2d-js反编译工具",
                    "网络抓包工具 (Burp Suite/Charles)"
                ]
            },
            "next_steps": [
                "运行Frida脚本进行动态分析",
                "分析libthemis.so的导出函数",
                "反编译Cocos2d-js脚本文件",
                "监控实际的网络通信",
                "提取密钥生成和管理机制"
            ]
        }
        
        report_path = self.output_dir / "cipher_analysis_report.json"
        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"分析报告已保存: {report_path}")
        return report
    
    def run_complete_analysis(self):
        """运行完整分析"""
        print("🔐 cipherTxt逆向分析工具包")
        print("=" * 50)
        
        # 执行各项分析
        self.analyze_encrypted_strings()
        self.generate_cipher_test_cases()
        self.analyze_themis_patterns()
        self.create_frida_runner()
        report = self.generate_analysis_report()
        
        print("\n" + "=" * 50)
        print("📋 分析完成总结")
        print("=" * 50)
        
        print("✅ 已完成的分析:")
        print("  1. 字符串加密机制分析")
        print("  2. cipherTxt测试用例生成")
        print("  3. Themis加密模式分析")
        print("  4. Frida动态分析脚本")
        print("  5. 完整分析报告")
        
        print(f"\n📁 输出目录: {self.output_dir}")
        print("📄 生成的文件:")
        for file in self.output_dir.iterdir():
            print(f"  - {file.name}")
        
        print("\n🎯 下一步操作建议:")
        print("  1. 连接Android设备")
        print("  2. 安装目标APK")
        print("  3. 运行Frida分析脚本")
        print("  4. 分析动态Hook结果")
        print("  5. 根据结果调整分析策略")
        
        return report

def main():
    toolkit = CipherReverseToolkit()
    toolkit.run_complete_analysis()

if __name__ == "__main__":
    main()
