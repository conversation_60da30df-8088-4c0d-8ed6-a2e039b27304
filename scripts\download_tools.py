#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载反编译工具脚本
自动下载并配置apktool、jadx、dex2jar等工具
"""

import os
import sys
import requests
import zipfile
import shutil
from pathlib import Path
from urllib.parse import urlparse

class ToolDownloader:
    def __init__(self):
        self.tools_dir = Path("tools")
        self.tools_dir.mkdir(exist_ok=True)
        
        # 工具下载链接
        self.tool_urls = {
            "apktool": {
                "url": "https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/linux/apktool",
                "jar_url": "https://bitbucket.org/iBotPeaches/apktool/downloads/apktool_2.8.1.jar",
                "filename": "apktool.jar"
            },
            "jadx": {
                "url": "https://github.com/skylot/jadx/releases/download/v1.4.7/jadx-1.4.7.zip",
                "filename": "jadx.zip"
            },
            "dex2jar": {
                "url": "https://github.com/pxb1988/dex2jar/releases/download/v2.4/dex2jar-2.4.zip",
                "filename": "dex2jar.zip"
            }
        }
    
    def download_file(self, url, filename):
        """下载文件"""
        print(f"正在下载: {url}")
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            file_path = self.tools_dir / filename
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print(f"下载完成: {file_path}")
            return file_path
        
        except Exception as e:
            print(f"下载失败: {e}")
            return None
    
    def extract_zip(self, zip_path, extract_to):
        """解压ZIP文件"""
        print(f"正在解压: {zip_path}")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
            
            print(f"解压完成: {extract_to}")
            return True
        
        except Exception as e:
            print(f"解压失败: {e}")
            return False
    
    def download_apktool(self):
        """下载apktool"""
        print("\n=== 下载apktool ===")
        
        # 下载apktool jar文件
        jar_path = self.download_file(
            self.tool_urls["apktool"]["jar_url"],
            self.tool_urls["apktool"]["filename"]
        )
        
        if jar_path:
            print("apktool下载成功")
            return True
        
        return False
    
    def download_jadx(self):
        """下载jadx"""
        print("\n=== 下载jadx ===")
        
        zip_path = self.download_file(
            self.tool_urls["jadx"]["url"],
            self.tool_urls["jadx"]["filename"]
        )
        
        if zip_path:
            # 解压jadx
            jadx_dir = self.tools_dir / "jadx"
            if self.extract_zip(zip_path, jadx_dir):
                # 删除zip文件
                zip_path.unlink()
                print("jadx安装成功")
                return True
        
        return False
    
    def download_dex2jar(self):
        """下载dex2jar"""
        print("\n=== 下载dex2jar ===")
        
        zip_path = self.download_file(
            self.tool_urls["dex2jar"]["url"],
            self.tool_urls["dex2jar"]["filename"]
        )
        
        if zip_path:
            # 解压dex2jar
            temp_dir = self.tools_dir / "temp"
            if self.extract_zip(zip_path, temp_dir):
                # 移动到正确位置
                extracted_dirs = list(temp_dir.glob("dex2jar-*"))
                if extracted_dirs:
                    dex2jar_dir = self.tools_dir / "dex2jar"
                    if dex2jar_dir.exists():
                        shutil.rmtree(dex2jar_dir)
                    shutil.move(str(extracted_dirs[0]), str(dex2jar_dir))
                
                # 清理临时文件
                shutil.rmtree(temp_dir)
                zip_path.unlink()
                print("dex2jar安装成功")
                return True
        
        return False
    
    def create_batch_files(self):
        """创建Windows批处理文件"""
        print("\n=== 创建批处理文件 ===")
        
        # 创建apktool.bat
        apktool_bat = self.tools_dir / "apktool.bat"
        with open(apktool_bat, 'w') as f:
            f.write('@echo off\n')
            f.write('java -jar "%~dp0apktool.jar" %*\n')
        
        # 修改jadx.bat路径
        jadx_bat = self.tools_dir / "jadx" / "bin" / "jadx.bat"
        if jadx_bat.exists():
            print("jadx.bat已存在")
        
        # 修改dex2jar批处理文件权限
        dex2jar_dir = self.tools_dir / "dex2jar"
        if dex2jar_dir.exists():
            for bat_file in dex2jar_dir.glob("*.bat"):
                print(f"找到dex2jar批处理文件: {bat_file}")
        
        print("批处理文件创建完成")
    
    def verify_tools(self):
        """验证工具是否正确安装"""
        print("\n=== 验证工具安装 ===")
        
        tools_status = {}
        
        # 检查apktool
        apktool_jar = self.tools_dir / "apktool.jar"
        tools_status["apktool"] = apktool_jar.exists()
        
        # 检查jadx
        jadx_bat = self.tools_dir / "jadx" / "bin" / "jadx.bat"
        tools_status["jadx"] = jadx_bat.exists()
        
        # 检查dex2jar
        dex2jar_bat = self.tools_dir / "dex2jar" / "d2j-dex2jar.bat"
        tools_status["dex2jar"] = dex2jar_bat.exists()
        
        print("工具安装状态:")
        for tool, status in tools_status.items():
            status_text = "✓ 已安装" if status else "✗ 未安装"
            print(f"  {tool}: {status_text}")
        
        return all(tools_status.values())
    
    def run(self):
        """运行下载流程"""
        print("开始下载反编译工具...")
        
        success_count = 0
        
        if self.download_apktool():
            success_count += 1
        
        if self.download_jadx():
            success_count += 1
        
        if self.download_dex2jar():
            success_count += 1
        
        self.create_batch_files()
        
        if self.verify_tools():
            print("\n所有工具下载并配置成功！")
        else:
            print(f"\n部分工具下载成功 ({success_count}/3)")
            print("请检查网络连接或手动下载缺失的工具")

def main():
    downloader = ToolDownloader()
    downloader.run()

if __name__ == "__main__":
    main()
