#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战斗收益结算机制分析脚本
分析游戏的战斗收益是本地结算还是服务器结算
"""

import json
import re
from pathlib import Path
from collections import defaultdict

class BattleSettlementAnalyzer:
    def __init__(self):
        self.analysis_file = Path("output/analysis/com.legend.startknife.tap_75_analysis.json")
        
    def load_analysis_data(self):
        """加载分析数据"""
        if not self.analysis_file.exists():
            print("❌ 分析文件不存在，请先运行 APK 分析")
            return None
        
        with open(self.analysis_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_network_components(self, data):
        """分析网络通信组件"""
        print("🌐 网络通信组件分析")
        print("=" * 50)
        
        # 分析原生库中的网络组件
        lib_info = data.get("native_libraries", {})
        network_libs = []
        
        for arch_libs in lib_info.get("libraries", {}).values():
            for lib in arch_libs:
                if any(keyword in lib.lower() for keyword in ['network', 'net', 'http', 'socket', 'connect']):
                    if lib not in network_libs:
                        network_libs.append(lib)
        
        # 分析资源文件中的网络相关文件
        assets = data.get("resources", {}).get("assets", [])
        network_assets = []
        
        for asset in assets:
            if any(keyword in asset.lower() for keyword in ['network', 'server', 'api', 'http', 'socket', 'connect', 'url']):
                network_assets.append(asset)
        
        print("📡 网络相关原生库:")
        if network_libs:
            for lib in network_libs:
                print(f"  - {lib}")
        else:
            print("  ❌ 未发现明显的网络通信原生库")
        
        print(f"\n📄 网络相关资源文件:")
        if network_assets:
            for asset in network_assets[:10]:  # 只显示前10个
                print(f"  - {asset}")
            if len(network_assets) > 10:
                print(f"  ... 还有 {len(network_assets) - 10} 个文件")
        else:
            print("  ❌ 未发现明显的网络配置文件")
        
        return network_libs, network_assets
    
    def analyze_data_storage(self, data):
        """分析数据存储机制"""
        print("\n💾 数据存储机制分析")
        print("=" * 50)
        
        # 分析存储相关的原生库
        lib_info = data.get("native_libraries", {})
        storage_libs = []
        
        for arch_libs in lib_info.get("libraries", {}).values():
            for lib in arch_libs:
                if any(keyword in lib.lower() for keyword in ['mmkv', 'db', 'sqlite', 'storage', 'data', 'save']):
                    if lib not in storage_libs:
                        storage_libs.append(lib)
        
        # 分析配置文件
        assets = data.get("resources", {}).get("assets", [])
        config_files = []
        data_files = []
        
        for asset in assets:
            if 'config.json' in asset:
                config_files.append(asset)
            elif any(keyword in asset.lower() for keyword in ['data', 'save', 'progress', 'user']):
                data_files.append(asset)
        
        print("🗄️ 存储相关原生库:")
        for lib in storage_libs:
            print(f"  - {lib}")
        
        print(f"\n📋 配置文件数量: {len(config_files)} 个")
        print("主要配置文件:")
        for config in config_files[:10]:
            print(f"  - {config}")
        
        return storage_libs, config_files
    
    def analyze_security_components(self, data):
        """分析安全组件"""
        print("\n🔒 安全组件分析")
        print("=" * 50)
        
        # 分析安全相关的原生库
        lib_info = data.get("native_libraries", {})
        security_libs = []
        
        for arch_libs in lib_info.get("libraries", {}).values():
            for lib in arch_libs:
                if any(keyword in lib.lower() for keyword in ['security', 'auth', 'encrypt', 'crypto', 'safe', 'protect']):
                    if lib not in security_libs:
                        security_libs.append(lib)
        
        # 分析安全相关资源
        assets = data.get("resources", {}).get("assets", [])
        security_assets = []
        
        for asset in assets:
            if any(keyword in asset.lower() for keyword in ['security', 'encrypt', 'auth', 'safe', 'protect']):
                security_assets.append(asset)
        
        print("🛡️ 安全相关原生库:")
        for lib in security_libs:
            print(f"  - {lib}")
        
        print(f"\n🔐 安全相关资源文件:")
        for asset in security_assets:
            print(f"  - {asset}")
        
        return security_libs, security_assets
    
    def analyze_third_party_sdks(self, data):
        """分析第三方SDK的网络能力"""
        print("\n🔌 第三方SDK网络能力分析")
        print("=" * 50)
        
        # SDK网络能力分析
        sdk_network_capabilities = {
            "阿里云SDK": {
                "libs": ["libalicomphonenumberauthsdk_core.so", "libpns-2.13.4-*.so"],
                "capabilities": ["手机号认证", "推送服务", "云端验证"],
                "network_required": True
            },
            "友盟SDK": {
                "libs": ["libumeng-spy.so"],
                "capabilities": ["数据统计", "用户行为分析", "事件上报"],
                "network_required": True
            },
            "TapTap SDK": {
                "libs": ["libtapsdkcore.so"],
                "capabilities": ["用户登录", "成就系统", "排行榜"],
                "network_required": True
            },
            "穿山甲广告SDK": {
                "libs": ["libpangleflipped.so"],
                "capabilities": ["广告请求", "广告展示", "收益统计"],
                "network_required": True
            },
            "MMKV存储": {
                "libs": ["libmmkv.so"],
                "capabilities": ["本地数据存储", "配置缓存"],
                "network_required": False
            }
        }
        
        # 检测已集成的SDK
        lib_info = data.get("native_libraries", {})
        all_libs = []
        for arch_libs in lib_info.get("libraries", {}).values():
            all_libs.extend(arch_libs)
        
        detected_sdks = []
        for sdk_name, sdk_info in sdk_network_capabilities.items():
            for lib_pattern in sdk_info["libs"]:
                if any(lib_pattern.replace("*", "") in lib for lib in all_libs):
                    detected_sdks.append((sdk_name, sdk_info))
                    break
        
        print("📊 检测到的SDK及其网络能力:")
        for sdk_name, sdk_info in detected_sdks:
            network_status = "🌐 需要网络" if sdk_info["network_required"] else "📱 本地处理"
            print(f"\n  {sdk_name} - {network_status}")
            for capability in sdk_info["capabilities"]:
                print(f"    • {capability}")
        
        return detected_sdks
    
    def analyze_battle_system_structure(self, data):
        """分析战斗系统结构"""
        print("\n⚔️ 战斗系统结构分析")
        print("=" * 50)
        
        assets = data.get("resources", {}).get("assets", [])
        
        # 分析Boss战斗相关文件
        boss_configs = []
        boss_data = defaultdict(list)
        
        for asset in assets:
            if 'Boss' in asset and 'config.json' in asset:
                boss_configs.append(asset)
            elif 'Boss' in asset:
                boss_match = re.search(r'Boss(\d+)', asset)
                if boss_match:
                    boss_num = boss_match.group(1)
                    boss_data[f"Boss{boss_num}"].append(asset)
        
        print("🎮 Boss战斗配置文件:")
        for config in boss_configs:
            print(f"  - {config}")
        
        print(f"\n📊 各Boss资源统计:")
        for boss, files in sorted(boss_data.items()):
            print(f"  {boss}: {len(files)} 个文件")
        
        # 分析数据表
        data_table_files = [asset for asset in assets if 'DataTable' in asset]
        print(f"\n📋 数据表文件: {len(data_table_files)} 个")
        
        return boss_configs, boss_data, data_table_files
    
    def determine_settlement_mechanism(self, network_libs, network_assets, security_libs, detected_sdks, boss_data):
        """判断结算机制"""
        print("\n🎯 战斗收益结算机制判断")
        print("=" * 50)
        
        # 评分系统
        local_score = 0
        server_score = 0
        
        # 1. 网络组件分析
        if not network_libs and len(network_assets) < 5:
            local_score += 3
            print("📱 网络组件较少，倾向本地结算 (+3)")
        else:
            server_score += 2
            print("🌐 存在网络组件，可能有服务器通信 (+2)")
        
        # 2. 安全组件分析
        if len(security_libs) >= 3:
            server_score += 3
            print("🔒 多重安全组件，可能需要服务器验证 (+3)")
        else:
            local_score += 1
            print("🔓 安全组件较少，可能本地处理 (+1)")
        
        # 3. SDK网络能力分析
        network_required_sdks = [sdk for sdk, info in detected_sdks if info["network_required"]]
        if len(network_required_sdks) >= 3:
            server_score += 4
            print(f"📡 {len(network_required_sdks)}个网络SDK，强烈倾向服务器结算 (+4)")
        else:
            local_score += 2
            print("📱 网络SDK较少，可能本地结算 (+2)")
        
        # 4. 游戏复杂度分析
        total_boss_files = sum(len(files) for files in boss_data.values())
        if total_boss_files > 1000:
            local_score += 2
            print("🎮 大量本地资源，支持本地计算 (+2)")
        
        # 5. 数据存储分析 (MMKV存在表明有本地存储能力)
        mmkv_detected = any("mmkv" in sdk.lower() for sdk, _ in detected_sdks)
        if mmkv_detected:
            local_score += 2
            print("💾 MMKV本地存储，支持本地数据处理 (+2)")
        
        print(f"\n📊 评分结果:")
        print(f"  本地结算倾向: {local_score} 分")
        print(f"  服务器结算倾向: {server_score} 分")
        
        # 判断结果
        if server_score > local_score + 2:
            result = "🌐 服务器结算"
            confidence = "高"
        elif local_score > server_score + 2:
            result = "📱 本地结算"
            confidence = "高"
        else:
            result = "🔄 混合结算"
            confidence = "中"
        
        print(f"\n🎯 结论: {result} (置信度: {confidence})")
        
        return result, confidence, local_score, server_score
    
    def generate_detailed_analysis(self, result, confidence, detected_sdks):
        """生成详细分析报告"""
        print(f"\n📋 详细分析报告")
        print("=" * 50)
        
        if "服务器结算" in result:
            print("🌐 服务器结算特征:")
            print("  • 多个需要网络的第三方SDK")
            print("  • 强安全组件 (防作弊)")
            print("  • 用户认证和数据统计")
            print("  • 可能的反作弊机制")
            
            print(f"\n💡 推测机制:")
            print("  1. 战斗过程在本地进行")
            print("  2. 战斗结果发送到服务器验证")
            print("  3. 服务器计算最终收益")
            print("  4. 防止客户端作弊")
            
        elif "本地结算" in result:
            print("📱 本地结算特征:")
            print("  • 大量本地游戏资源")
            print("  • 本地存储能力强")
            print("  • 网络组件相对较少")
            print("  • 可能为单机游戏")
            
            print(f"\n💡 推测机制:")
            print("  1. 战斗和结算都在本地进行")
            print("  2. 数据保存在本地存储")
            print("  3. 可能只在特定时机同步数据")
            print("  4. 注重游戏体验流畅性")
            
        else:  # 混合结算
            print("🔄 混合结算特征:")
            print("  • 既有本地处理能力又有网络能力")
            print("  • 可能根据不同情况采用不同策略")
            
            print(f"\n💡 推测机制:")
            print("  1. 普通战斗可能本地结算")
            print("  2. 重要奖励服务器验证")
            print("  3. 定期数据同步")
            print("  4. 平衡体验和安全")
        
        print(f"\n🔍 关键证据:")
        network_sdks = [sdk_name for sdk_name, info in detected_sdks if info["network_required"]]
        print(f"  • 网络SDK数量: {len(network_sdks)}")
        print(f"  • 主要网络服务: {', '.join(network_sdks[:3])}")
    
    def run(self):
        """运行战斗结算分析"""
        print("⚔️ 战斗收益结算机制分析")
        print("=" * 60)
        
        data = self.load_analysis_data()
        if not data:
            return
        
        # 执行各项分析
        network_libs, network_assets = self.analyze_network_components(data)
        storage_libs, config_files = self.analyze_data_storage(data)
        security_libs, security_assets = self.analyze_security_components(data)
        detected_sdks = self.analyze_third_party_sdks(data)
        boss_configs, boss_data, data_table_files = self.analyze_battle_system_structure(data)
        
        # 判断结算机制
        result, confidence, local_score, server_score = self.determine_settlement_mechanism(
            network_libs, network_assets, security_libs, detected_sdks, boss_data
        )
        
        # 生成详细分析
        self.generate_detailed_analysis(result, confidence, detected_sdks)
        
        print("\n" + "=" * 60)
        print("✅ 战斗收益结算机制分析完成！")

def main():
    analyzer = BattleSettlementAnalyzer()
    analyzer.run()

if __name__ == "__main__":
    main()
