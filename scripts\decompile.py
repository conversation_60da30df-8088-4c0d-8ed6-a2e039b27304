#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK反编译主脚本
支持多种反编译工具和批量处理
"""

import os
import sys
import subprocess
import argparse
import json
import shutil
from pathlib import Path
from datetime import datetime

class APKDecompiler:
    def __init__(self, config_file="config/config.json"):
        self.config = self.load_config(config_file)
        self.tools_dir = Path("tools")
        self.output_dir = Path("output")
        self.apk_dir = Path("apk")
        
        # 确保目录存在
        self.output_dir.mkdir(exist_ok=True)
        
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"配置文件 {config_file} 不存在，使用默认配置")
            return self.get_default_config()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "tools": {
                "apktool": "tools/apktool.jar",
                "jadx": "tools/jadx/bin/jadx.bat",
                "dex2jar": "tools/dex2jar/d2j-dex2jar.bat"
            },
            "java_path": "java",
            "output_format": ["smali", "java", "resources"],
            "verbose": True
        }
    
    def find_apk_files(self):
        """查找APK文件"""
        apk_files = list(self.apk_dir.glob("*.apk"))
        if not apk_files:
            print("在apk目录中未找到APK文件")
            return []
        return apk_files
    
    def run_command(self, cmd, cwd=None):
        """执行命令"""
        if self.config.get("verbose", True):
            print(f"执行命令: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=cwd,
                capture_output=True, 
                text=True, 
                encoding='utf-8'
            )
            
            if result.returncode != 0:
                print(f"命令执行失败: {result.stderr}")
                return False
            
            if self.config.get("verbose", True) and result.stdout:
                print(result.stdout)
            
            return True
        except Exception as e:
            print(f"执行命令时出错: {e}")
            return False
    
    def decompile_with_apktool(self, apk_path, output_path):
        """使用apktool反编译"""
        print(f"\n使用apktool反编译: {apk_path.name}")
        
        apktool_path = self.tools_dir / "apktool.jar"
        if not apktool_path.exists():
            print(f"apktool工具不存在: {apktool_path}")
            return False
        
        cmd = [
            self.config["java_path"], 
            "-jar", 
            str(apktool_path),
            "d",
            str(apk_path),
            "-o",
            str(output_path / "apktool")
        ]
        
        return self.run_command(cmd)
    
    def decompile_with_jadx(self, apk_path, output_path):
        """使用jadx反编译"""
        print(f"\n使用jadx反编译: {apk_path.name}")
        
        jadx_path = self.tools_dir / "jadx" / "bin" / "jadx.bat"
        if not jadx_path.exists():
            print(f"jadx工具不存在: {jadx_path}")
            return False
        
        cmd = [
            str(jadx_path),
            "-d",
            str(output_path / "jadx"),
            str(apk_path)
        ]
        
        return self.run_command(cmd)
    
    def decompile_with_dex2jar(self, apk_path, output_path):
        """使用dex2jar转换"""
        print(f"\n使用dex2jar转换: {apk_path.name}")
        
        dex2jar_path = self.tools_dir / "dex2jar" / "d2j-dex2jar.bat"
        if not dex2jar_path.exists():
            print(f"dex2jar工具不存在: {dex2jar_path}")
            return False
        
        jar_output = output_path / "dex2jar" / f"{apk_path.stem}.jar"
        jar_output.parent.mkdir(parents=True, exist_ok=True)
        
        cmd = [
            str(dex2jar_path),
            str(apk_path),
            "-o",
            str(jar_output)
        ]
        
        return self.run_command(cmd)
    
    def generate_report(self, apk_path, output_path, results):
        """生成反编译报告"""
        report = {
            "apk_file": str(apk_path),
            "decompile_time": datetime.now().isoformat(),
            "results": results,
            "output_directory": str(output_path)
        }
        
        report_file = output_path / "decompile_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n反编译报告已生成: {report_file}")
    
    def decompile_apk(self, apk_path):
        """反编译单个APK文件"""
        print(f"\n开始反编译: {apk_path.name}")
        
        # 创建输出目录
        output_path = self.output_dir / apk_path.stem
        output_path.mkdir(exist_ok=True)
        
        results = {}
        
        # 使用不同工具反编译
        if "smali" in self.config.get("output_format", []):
            results["apktool"] = self.decompile_with_apktool(apk_path, output_path)
        
        if "java" in self.config.get("output_format", []):
            results["jadx"] = self.decompile_with_jadx(apk_path, output_path)
        
        results["dex2jar"] = self.decompile_with_dex2jar(apk_path, output_path)
        
        # 生成报告
        self.generate_report(apk_path, output_path, results)
        
        return results
    
    def run(self, apk_file=None):
        """运行反编译流程"""
        print("APK反编译工具启动")
        
        if apk_file:
            apk_files = [Path(apk_file)]
        else:
            apk_files = self.find_apk_files()
        
        if not apk_files:
            print("没有找到要反编译的APK文件")
            return
        
        for apk_path in apk_files:
            try:
                self.decompile_apk(apk_path)
                print(f"\n{apk_path.name} 反编译完成")
            except Exception as e:
                print(f"反编译 {apk_path.name} 时出错: {e}")
        
        print("\n所有APK文件反编译完成")

def main():
    parser = argparse.ArgumentParser(description="APK反编译工具")
    parser.add_argument("--apk", help="指定要反编译的APK文件")
    parser.add_argument("--config", default="config/config.json", help="配置文件路径")
    
    args = parser.parse_args()
    
    decompiler = APKDecompiler(args.config)
    decompiler.run(args.apk)

if __name__ == "__main__":
    main()
