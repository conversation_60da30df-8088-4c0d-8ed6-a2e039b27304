#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
cipherTxt解密脚本
尝试多种解密方法来解密真实的cipherTxt
"""

import base64
import json
import hashlib
import hmac
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import binascii

class CipherTxtDecryptor:
    def __init__(self):
        # 真实的cipherTxt数据
        self.cipher_txt = "LHCSyzsU4hRsWMx3pBLxDzNumOAwdDBaNE51CnpMRm2pfLIxOOibgYI/57zY5JMcEC6gRr5c/mJCQDHgUa3UJ6ToVUui7fVheP3l/ip1PXjeTX/DTLqZwkF1Plfp1uICv0jk2d8CFoYMlac5TNa3heG2zv7fnaWB87L9xbwR3JB4GBbUWR+mX77NhpK5f7207JtgJLd5EUZfUHMRbn0LhuDaWAAnYRRS/pLxG3urQdJTD/IdGseHGbwqUpBJwy22CcfwI4MHisn1KiAa4Uw98G4ULtyBG0LmBJfBfSK19mbusgceot+/iVfGJcOi3KES8Lb2jAehNboM90FeI+dexI8t0LfpZWAobimDXOg+fcFKw+uk6LTsUGovWAx7ONn6d/qKuWTYOFQmf+c3Qlrh7WcDjGx+1wFbLRg0fYsRtZOTpG6fRNwXIDstofZ70Ok+muWf/pc8NjSCpvflJHK+oRRqfNnOopnpqY67wABtmy9PeK/B4BDvkc0IbqhFJ2bZ"
        
        # 请求参数 (可能用于密钥生成)
        self.request_data = {
            "fileId": "1",
            "nonceStr": "WYSTYJ", 
            "channel": "appstore",
            "udid": "com.dylantest.testdylanF8A14DDA-78DE-4348-9DFC-750E759630B6",
            "productId": "edd4a78f-2957-4020-a80d-ea221b6d1052",
            "osType": "ios",
            "token": "20240902211141001375268527046659",
            "location": "1",
            "sign": "B38FACB8CEA745A35EFE799F70C6B36E",
            "equipId": "10",
            "count": "1",
            "slotId": "0",
            "version": "177",
            "timestamp": "1756648587",
            "vestId": "12373237-b98e-40a5-ba47-44d3d977ba5e"
        }
        
    def analyze_cipher_structure(self):
        """分析cipherTxt的结构"""
        print("🔍 分析cipherTxt结构")
        print("=" * 50)
        
        # Base64解码
        try:
            cipher_bytes = base64.b64decode(self.cipher_txt)
            print(f"✅ Base64解码成功")
            print(f"   原始长度: {len(self.cipher_txt)} 字符")
            print(f"   解码长度: {len(cipher_bytes)} 字节")
            print(f"   十六进制: {cipher_bytes[:32].hex()}...")
            
            # 分析可能的结构
            print(f"\n📊 结构分析:")
            print(f"   前16字节 (可能是IV): {cipher_bytes[:16].hex()}")
            print(f"   后16字节 (可能是Tag): {cipher_bytes[-16:].hex()}")
            print(f"   中间数据长度: {len(cipher_bytes) - 32} 字节")
            
            return cipher_bytes
        except Exception as e:
            print(f"❌ Base64解码失败: {e}")
            return None
    
    def xor_decrypt(self, data, key=16):
        """XOR解密 (基于发现的算法)"""
        try:
            if isinstance(data, str):
                result = ""
                for char in data:
                    result += chr(ord(char) ^ key)
                return result
            else:
                result = bytearray()
                for byte in data:
                    result.append(byte ^ key)
                return bytes(result)
        except Exception as e:
            print(f"XOR解密失败: {e}")
            return None
    
    def try_simple_decryption(self, cipher_bytes):
        """尝试简单解密方法"""
        print("\n🔧 尝试简单解密方法")
        print("=" * 50)
        
        methods = []
        
        # 1. 直接XOR解密
        try:
            xor_result = self.xor_decrypt(cipher_bytes, 16)
            if xor_result:
                # 尝试解析为文本
                try:
                    text_result = xor_result.decode('utf-8', errors='ignore')
                    if any(c.isprintable() for c in text_result[:100]):
                        methods.append(("XOR 16", text_result[:200]))
                except:
                    pass
        except Exception as e:
            print(f"XOR解密失败: {e}")
        
        # 2. 尝试不同的XOR密钥
        for key in [1, 8, 16, 32, 64, 128, 255]:
            try:
                xor_result = self.xor_decrypt(cipher_bytes, key)
                if xor_result:
                    try:
                        text_result = xor_result.decode('utf-8', errors='ignore')
                        if text_result.startswith('{') or 'action' in text_result:
                            methods.append((f"XOR {key}", text_result[:200]))
                    except:
                        pass
            except:
                pass
        
        # 3. 尝试Base64双重解码
        try:
            # 先XOR再Base64解码
            xor_result = self.xor_decrypt(self.cipher_txt, 16)
            if xor_result:
                try:
                    double_decode = base64.b64decode(xor_result)
                    text_result = double_decode.decode('utf-8', errors='ignore')
                    if any(c.isprintable() for c in text_result):
                        methods.append(("XOR+Base64", text_result[:200]))
                except:
                    pass
        except:
            pass
        
        # 显示结果
        if methods:
            print("✅ 发现可能的解密结果:")
            for method, result in methods:
                print(f"\n{method}:")
                print(f"  {result}")
        else:
            print("❌ 简单方法未能解密")
        
        return methods
    
    def try_aes_decryption(self, cipher_bytes):
        """尝试AES解密"""
        print("\n🔐 尝试AES解密")
        print("=" * 50)
        
        # 生成可能的密钥
        possible_keys = []
        
        # 1. 基于请求参数生成密钥
        key_materials = [
            self.request_data["token"],
            self.request_data["udid"],
            self.request_data["nonceStr"],
            self.request_data["sign"],
            f"{self.request_data['token']}{self.request_data['nonceStr']}",
            f"{self.request_data['udid']}{self.request_data['timestamp']}",
        ]
        
        for material in key_materials:
            # SHA256哈希作为密钥
            key = hashlib.sha256(material.encode()).digest()
            possible_keys.append((f"SHA256({material[:20]}...)", key))
            
            # MD5哈希 (16字节，需要扩展到32字节)
            md5_key = hashlib.md5(material.encode()).digest()
            extended_key = md5_key + md5_key  # 扩展到32字节
            possible_keys.append((f"MD5x2({material[:20]}...)", extended_key))
        
        # 2. 固定密钥尝试
        fixed_keys = [
            ("固定密钥1", b"1234567890123456" * 2),
            ("固定密钥2", b"abcdefghijklmnop" * 2),
            ("零密钥", b"\x00" * 32),
        ]
        possible_keys.extend(fixed_keys)
        
        results = []
        
        for key_name, key in possible_keys:
            try:
                # 尝试不同的AES模式
                modes = [
                    ("AES-GCM", self._try_aes_gcm),
                    ("AES-CBC", self._try_aes_cbc),
                    ("AES-ECB", self._try_aes_ecb),
                ]
                
                for mode_name, decrypt_func in modes:
                    try:
                        result = decrypt_func(cipher_bytes, key)
                        if result:
                            results.append((f"{key_name} + {mode_name}", result))
                    except Exception as e:
                        continue
                        
            except Exception as e:
                continue
        
        if results:
            print("✅ AES解密结果:")
            for method, result in results:
                print(f"\n{method}:")
                print(f"  {result[:200]}")
        else:
            print("❌ AES解密未成功")
        
        return results
    
    def _try_aes_gcm(self, cipher_bytes, key):
        """尝试AES-GCM解密"""
        if len(cipher_bytes) < 28:  # 至少需要IV(12) + Tag(16)
            return None
            
        # 假设前12字节是IV，后16字节是认证标签
        iv = cipher_bytes[:12]
        tag = cipher_bytes[-16:]
        ciphertext = cipher_bytes[12:-16]
        
        try:
            cipher = AES.new(key[:32], AES.MODE_GCM, nonce=iv)
            plaintext = cipher.decrypt_and_verify(ciphertext, tag)
            return plaintext.decode('utf-8', errors='ignore')
        except:
            # 尝试前16字节作为IV
            if len(cipher_bytes) >= 32:
                iv = cipher_bytes[:16]
                tag = cipher_bytes[-16:]
                ciphertext = cipher_bytes[16:-16]
                try:
                    cipher = AES.new(key[:32], AES.MODE_GCM, nonce=iv[:12])
                    plaintext = cipher.decrypt_and_verify(ciphertext, tag)
                    return plaintext.decode('utf-8', errors='ignore')
                except:
                    pass
            return None
    
    def _try_aes_cbc(self, cipher_bytes, key):
        """尝试AES-CBC解密"""
        if len(cipher_bytes) < 32:  # 至少需要IV(16) + 一个块(16)
            return None
            
        iv = cipher_bytes[:16]
        ciphertext = cipher_bytes[16:]
        
        try:
            cipher = AES.new(key[:32], AES.MODE_CBC, iv)
            plaintext = cipher.decrypt(ciphertext)
            # 尝试去除填充
            try:
                plaintext = unpad(plaintext, 16)
            except:
                pass
            return plaintext.decode('utf-8', errors='ignore')
        except:
            return None
    
    def _try_aes_ecb(self, cipher_bytes, key):
        """尝试AES-ECB解密"""
        try:
            cipher = AES.new(key[:32], AES.MODE_ECB)
            plaintext = cipher.decrypt(cipher_bytes)
            # 尝试去除填充
            try:
                plaintext = unpad(plaintext, 16)
            except:
                pass
            return plaintext.decode('utf-8', errors='ignore')
        except:
            return None
    
    def analyze_request_pattern(self):
        """分析请求模式，寻找加密线索"""
        print("\n🔍 分析请求模式")
        print("=" * 50)
        
        print("📋 请求参数分析:")
        for key, value in self.request_data.items():
            print(f"  {key}: {value}")
        
        # 分析sign字段 (可能是HMAC)
        print(f"\n🔐 签名分析:")
        print(f"  sign: {self.request_data['sign']}")
        print(f"  长度: {len(self.request_data['sign'])} (MD5: 32, SHA1: 40, SHA256: 64)")
        
        # 尝试验证签名
        sign_data = ""
        for key in sorted(self.request_data.keys()):
            if key not in ['sign', 'cipherTxt']:
                sign_data += f"{key}={self.request_data[key]}&"
        sign_data = sign_data.rstrip('&')
        
        print(f"  签名数据: {sign_data}")
        
        # 尝试不同的签名算法
        possible_secrets = ["secret", "key", "123456", self.request_data["token"][:16]]
        for secret in possible_secrets:
            md5_sign = hashlib.md5((sign_data + secret).encode()).hexdigest().upper()
            if md5_sign == self.request_data['sign']:
                print(f"  ✅ 找到签名密钥: {secret}")
                return secret
        
        print(f"  ❌ 未找到签名密钥")
        return None
    
    def run_complete_analysis(self):
        """运行完整的解密分析"""
        print("🔐 cipherTxt解密分析")
        print("=" * 60)
        
        # 1. 分析结构
        cipher_bytes = self.analyze_cipher_structure()
        if not cipher_bytes:
            return
        
        # 2. 分析请求模式
        sign_key = self.analyze_request_pattern()
        
        # 3. 尝试简单解密
        simple_results = self.try_simple_decryption(cipher_bytes)
        
        # 4. 尝试AES解密
        aes_results = self.try_aes_decryption(cipher_bytes)
        
        # 5. 总结
        print("\n" + "=" * 60)
        print("📋 解密分析总结")
        print("=" * 60)
        
        all_results = simple_results + aes_results
        if all_results:
            print("✅ 发现可能的解密结果:")
            for i, (method, result) in enumerate(all_results, 1):
                print(f"\n{i}. {method}:")
                print(f"   {result[:100]}...")
        else:
            print("❌ 所有尝试的解密方法都失败了")
            print("\n💡 建议:")
            print("  1. 这可能是Themis Secure Cell加密")
            print("  2. 需要正确的密钥和IV")
            print("  3. 可能需要动态分析获取密钥")
            print("  4. 密钥可能基于设备特征生成")

def main():
    decryptor = CipherTxtDecryptor()
    decryptor.run_complete_analysis()

if __name__ == "__main__":
    main()
