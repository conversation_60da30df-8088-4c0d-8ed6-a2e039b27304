/**
 * Frida脚本 - cipherTxt生成逻辑动态分析
 * 用于Hook关键函数并监控加密过程
 */

console.log("[+] cipherTxt动态分析脚本启动");

// 1. Hook JNI函数调用
function hookJNI() {
    console.log("[+] 开始Hook JNI调用...");
    
    // Hook JNI GetStringUTFChars - 监控字符串传递
    var GetStringUTFChars = Module.findExportByName("libart.so", "_ZN3art3JNI17GetStringUTFCharsEP7_JNIEnvP8_jstringPh");
    if (GetStringUTFChars) {
        Interceptor.attach(GetStringUTFChars, {
            onEnter: function(args) {
                this.jstring = args[1];
            },
            onLeave: function(retval) {
                if (retval.isNull()) return;
                var str = retval.readCString();
                if (str && (str.includes("cipher") || str.includes("encrypt") || str.includes("http"))) {
                    console.log("[JNI] GetStringUTFChars: " + str);
                }
            }
        });
    }
    
    // Hook JNI CallStaticObjectMethod - 监控静态方法调用
    var CallStaticObjectMethod = Module.findExportByName("libart.so", "_ZN3art3JNI21CallStaticObjectMethodEP7_JNIEnvP7_jclassP10_jmethodIDz");
    if (CallStaticObjectMethod) {
        Interceptor.attach(CallStaticObjectMethod, {
            onEnter: function(args) {
                console.log("[JNI] CallStaticObjectMethod called");
            }
        });
    }
}

// 2. Hook Themis加密库函数
function hookThemis() {
    console.log("[+] 开始Hook Themis加密库...");
    
    var libthemis = Process.findModuleByName("libthemis.so");
    if (libthemis) {
        console.log("[+] 找到libthemis.so: " + libthemis.base);
        
        // Hook AES加密函数
        var aes_encrypt_symbols = [
            "themis_secure_cell_encrypt_seal",
            "themis_secure_cell_encrypt_context_imprint",
            "themis_secure_cell_encrypt_token_protect"
        ];
        
        aes_encrypt_symbols.forEach(function(symbol) {
            var addr = Module.findExportByName("libthemis.so", symbol);
            if (addr) {
                console.log("[+] Hook " + symbol + " at " + addr);
                Interceptor.attach(addr, {
                    onEnter: function(args) {
                        console.log("[Themis] " + symbol + " called");
                        console.log("  - Key length: " + args[1]);
                        console.log("  - Data length: " + args[3]);
                        
                        // 打印输入数据
                        if (args[2] && args[3].toInt32() > 0 && args[3].toInt32() < 1024) {
                            var data = args[2].readByteArray(Math.min(args[3].toInt32(), 256));
                            console.log("  - Input data: " + hexdump(data, {length: 64}));
                        }
                    },
                    onLeave: function(retval) {
                        console.log("[Themis] " + symbol + " returned: " + retval);
                    }
                });
            }
        });
        
        // Hook RSA函数
        var rsa_symbols = [
            "themis_secure_message_encrypt",
            "themis_secure_message_decrypt"
        ];
        
        rsa_symbols.forEach(function(symbol) {
            var addr = Module.findExportByName("libthemis.so", symbol);
            if (addr) {
                console.log("[+] Hook " + symbol + " at " + addr);
                Interceptor.attach(addr, {
                    onEnter: function(args) {
                        console.log("[Themis RSA] " + symbol + " called");
                    }
                });
            }
        });
    } else {
        console.log("[-] libthemis.so not found");
    }
}

// 3. Hook 360加固相关函数
function hook360() {
    console.log("[+] 开始Hook 360加固函数...");
    
    var libjiagu = Process.findModuleByName("libjiagu.so");
    if (libjiagu) {
        console.log("[+] 找到libjiagu.so: " + libjiagu.base);
        
        // Hook字符串解密函数
        Java.perform(function() {
            var UtilClass = Java.use("com.tianyu.util.a");
            if (UtilClass) {
                UtilClass.a.overload('java.lang.String').implementation = function(str) {
                    var result = this.a(str);
                    console.log("[360] String decrypt: '" + str + "' -> '" + result + "'");
                    return result;
                };
            }
        });
    }
}

// 4. Hook网络相关函数
function hookNetwork() {
    console.log("[+] 开始Hook网络函数...");
    
    // Hook HTTP相关的原生函数
    var symbols = [
        "send", "recv", "sendto", "recvfrom",
        "SSL_write", "SSL_read"
    ];
    
    symbols.forEach(function(symbol) {
        var addr = Module.findExportByName(null, symbol);
        if (addr) {
            Interceptor.attach(addr, {
                onEnter: function(args) {
                    if (symbol === "send" || symbol === "SSL_write") {
                        var data = args[1].readByteArray(Math.min(args[2].toInt32(), 512));
                        var str = args[1].readCString(Math.min(args[2].toInt32(), 512));
                        if (str && (str.includes("cipher") || str.includes("http") || str.includes("POST"))) {
                            console.log("[Network] " + symbol + " data:");
                            console.log(hexdump(data, {length: 256}));
                        }
                    }
                }
            });
        }
    });
    
    // Hook Java网络类
    Java.perform(function() {
        // Hook HttpURLConnection
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        if (HttpURLConnection) {
            HttpURLConnection.getOutputStream.implementation = function() {
                console.log("[Java Network] HttpURLConnection.getOutputStream called");
                return this.getOutputStream();
            };
        }
        
        // Hook OkHttp (如果使用)
        try {
            var OkHttpClient = Java.use("okhttp3.OkHttpClient");
            if (OkHttpClient) {
                console.log("[+] 找到OkHttp");
            }
        } catch (e) {
            // OkHttp not found
        }
    });
}

// 5. Hook Base64编码
function hookBase64() {
    console.log("[+] 开始Hook Base64编码...");
    
    Java.perform(function() {
        var Base64 = Java.use("android.util.Base64");
        if (Base64) {
            Base64.encodeToString.overload('[B', 'int').implementation = function(input, flags) {
                var result = this.encodeToString(input, flags);
                if (input.length > 10) {  // 只记录较长的数据
                    console.log("[Base64] Encode " + input.length + " bytes -> " + result.substring(0, 64) + "...");
                    console.log("  Input: " + Java.use("java.util.Arrays").toString(input));
                }
                return result;
            };
            
            Base64.decode.overload('java.lang.String', 'int').implementation = function(str, flags) {
                var result = this.decode(str, flags);
                if (str.length > 10) {
                    console.log("[Base64] Decode: " + str.substring(0, 64) + "...");
                }
                return result;
            };
        }
    });
}

// 6. Hook JavaScript层 (如果可能)
function hookJavaScript() {
    console.log("[+] 尝试Hook JavaScript层...");
    
    // 查找V8或JSC引擎
    var v8 = Process.findModuleByName("libv8.so");
    var jsc = Process.findModuleByName("libcocos2djs.so");
    
    if (v8) {
        console.log("[+] 找到V8引擎: " + v8.base);
    }
    
    if (jsc) {
        console.log("[+] 找到Cocos2d-js引擎: " + jsc.base);
        
        // 尝试Hook JavaScript函数调用
        var js_symbols = [
            "_ZN2v88internal7Runtime19kInlineRuntimeCallsE",
            "_ZN7cocos2d11ScriptEngine14executeStringEPKc"
        ];
        
        js_symbols.forEach(function(symbol) {
            var addr = Module.findExportByName("libcocos2djs.so", symbol);
            if (addr) {
                console.log("[+] Hook JS symbol: " + symbol);
                Interceptor.attach(addr, {
                    onEnter: function(args) {
                        console.log("[JS] " + symbol + " called");
                    }
                });
            }
        });
    }
}

// 主函数
function main() {
    console.log("[+] 开始cipherTxt动态分析...");
    
    // 等待应用启动
    Java.perform(function() {
        console.log("[+] Java环境就绪");
        
        // 执行各种Hook
        hookJNI();
        hookThemis();
        hook360();
        hookNetwork();
        hookBase64();
        hookJavaScript();
        
        console.log("[+] 所有Hook已设置完成");
        console.log("[+] 等待cipherTxt生成...");
    });
}

// 启动分析
main();
