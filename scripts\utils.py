#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
提供通用的工具函数
"""

import os
import sys
import platform
import subprocess
from pathlib import Path

def check_java_installation():
    """检查Java是否已安装"""
    try:
        result = subprocess.run(['java', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version_info = result.stderr.split('\n')[0]
            print(f"Java已安装: {version_info}")
            return True
        else:
            print("Java未正确安装")
            return False
    except FileNotFoundError:
        print("Java未安装或不在PATH中")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("需要Python 3.7或更高版本")
        return False

def get_system_info():
    """获取系统信息"""
    return {
        "platform": platform.platform(),
        "system": platform.system(),
        "architecture": platform.architecture()[0],
        "python_version": platform.python_version()
    }

def ensure_directory(path):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)

def get_file_size_human(size_bytes):
    """将字节大小转换为人类可读格式"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def print_banner():
    """打印程序横幅"""
    banner = """
    ╔══════════════════════════════════════╗
    ║           APK反编译工具              ║
    ║        APK Decompiler Tool           ║
    ╚══════════════════════════════════════╝
    """
    print(banner)

def print_separator(char="=", length=50):
    """打印分隔线"""
    print(char * length)

def validate_apk_file(file_path):
    """验证APK文件"""
    path = Path(file_path)
    
    if not path.exists():
        return False, "文件不存在"
    
    if not path.suffix.lower() == '.apk':
        return False, "不是APK文件"
    
    if path.stat().st_size == 0:
        return False, "文件为空"
    
    return True, "文件有效"

def check_tools_availability():
    """检查工具可用性"""
    tools_dir = Path("tools")
    tools_status = {}
    
    # 检查apktool
    apktool_path = tools_dir / "apktool.jar"
    tools_status["apktool"] = apktool_path.exists()
    
    # 检查jadx
    jadx_path = tools_dir / "jadx" / "bin" / "jadx.bat"
    tools_status["jadx"] = jadx_path.exists()
    
    # 检查dex2jar
    dex2jar_path = tools_dir / "dex2jar" / "d2j-dex2jar.bat"
    tools_status["dex2jar"] = dex2jar_path.exists()
    
    return tools_status

def print_system_check():
    """打印系统检查结果"""
    print_banner()
    print("系统环境检查:")
    print_separator("-")
    
    # 检查Python版本
    python_ok = check_python_version()
    
    # 检查Java安装
    java_ok = check_java_installation()
    
    # 检查工具可用性
    tools_status = check_tools_availability()
    print("\n工具可用性:")
    for tool, available in tools_status.items():
        status = "✓ 可用" if available else "✗ 不可用"
        print(f"  {tool}: {status}")
    
    # 系统信息
    sys_info = get_system_info()
    print(f"\n系统信息:")
    print(f"  平台: {sys_info['platform']}")
    print(f"  架构: {sys_info['architecture']}")
    
    print_separator()
    
    all_ready = python_ok and java_ok and all(tools_status.values())
    if all_ready:
        print("✓ 系统环境检查通过，可以开始使用")
    else:
        print("✗ 系统环境检查未通过，请安装缺失的组件")
    
    return all_ready
