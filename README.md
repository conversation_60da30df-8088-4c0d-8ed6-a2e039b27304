# APK反编译工程

这是一个用于反编译Android APK文件的工程，提供了完整的工具链和自动化脚本。

## 项目结构

```
decompileApk/
├── apk/                    # 存放待反编译的APK文件
├── tools/                  # 反编译工具
├── output/                 # 反编译输出目录
├── scripts/               # 自动化脚本
├── config/                # 配置文件
└── docs/                  # 文档
```

## 功能特性

- 支持多种反编译工具（apktool, jadx, dex2jar等）
- 自动化反编译流程
- 批量处理APK文件
- 生成详细的分析报告
- 支持资源文件提取
- 代码结构分析

## 快速开始

1. 将APK文件放入 `apk/` 目录
2. 运行反编译脚本：
   ```bash
   python scripts/decompile.py
   ```
3. 查看输出结果在 `output/` 目录

## 工具说明

- **apktool**: 用于反编译APK资源和smali代码
- **jadx**: 将dex文件反编译为Java源码
- **dex2jar**: 将dex文件转换为jar文件
- **aapt**: Android资源打包工具

## 依赖要求

- Python 3.7+
- Java 8+
- Android SDK (可选)

## 使用说明

详细使用说明请参考 `docs/` 目录下的文档。
