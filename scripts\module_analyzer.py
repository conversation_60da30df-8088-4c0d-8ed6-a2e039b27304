#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK模块功能分析脚本
深度分析APK中各个模块的功能实现
"""

import json
import re
from pathlib import Path
from collections import defaultdict, Counter

class ModuleAnalyzer:
    def __init__(self):
        self.analysis_file = Path("output/analysis/com.legend.startknife.tap_75_analysis.json")
        self.modules = defaultdict(list)
        self.sdk_modules = defaultdict(list)
        self.game_modules = defaultdict(list)
        
    def load_analysis_data(self):
        """加载分析数据"""
        if not self.analysis_file.exists():
            print("❌ 分析文件不存在，请先运行 APK 分析")
            return None
        
        with open(self.analysis_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_native_libraries(self, data):
        """分析原生库模块"""
        print("🔧 原生库模块分析")
        print("=" * 50)
        
        lib_info = data.get("native_libraries", {})
        libraries = lib_info.get("libraries", {})
        
        # 按功能分类原生库
        lib_categories = {
            "游戏引擎": ["libcocos2djs.so"],
            "网络通信": ["libipneigh-android.so"],
            "数据存储": ["libmmkv.so"],
            "安全认证": [
                "libalicomphonenumberauthsdk_core.so",
                "libmsaoaidauth.so", 
                "libmsaoaidsec.so",
                "libsecuritydevice.so",
                "libthemis.so"
            ],
            "广告SDK": [
                "libpangleflipped.so",
                "libsgcore.so"
            ],
            "统计分析": [
                "libumeng-spy.so"
            ],
            "推送服务": [
                "libpns-2.13.4-NologOnlineStandardCuumRelease_alijtca_plus.so",
                "libtapsdkcore.so"
            ],
            "动态加载": [
                "libzeus_direct_dex.so"
            ],
            "系统库": [
                "libc++_shared.so"
            ]
        }
        
        for arch, libs in libraries.items():
            print(f"\n📱 {arch} 架构:")
            for category, category_libs in lib_categories.items():
                found_libs = [lib for lib in libs if lib in category_libs]
                if found_libs:
                    print(f"  {category}: {len(found_libs)} 个")
                    for lib in found_libs:
                        print(f"    - {lib}")
    
    def analyze_assets_structure(self, data):
        """分析资源文件结构"""
        print("\n📁 游戏资源模块分析")
        print("=" * 50)
        
        assets = data.get("resources", {}).get("assets", [])
        
        # 分析游戏场景/关卡
        boss_scenes = defaultdict(int)
        resource_types = defaultdict(int)
        
        for asset in assets:
            # 分析Boss场景
            if "Boss" in asset:
                match = re.search(r'Boss(\d+)', asset)
                if match:
                    boss_num = match.group(1)
                    boss_scenes[f"Boss{boss_num}"] += 1
            
            # 分析资源类型
            if asset.endswith('.json'):
                if 'config.json' in asset:
                    resource_types['配置文件'] += 1
                elif 'import/' in asset:
                    resource_types['导入资源'] += 1
                else:
                    resource_types['其他JSON'] += 1
            elif asset.endswith('.png'):
                resource_types['图片资源'] += 1
            elif asset.endswith('.js') or asset.endswith('.jsc'):
                resource_types['脚本文件'] += 1
            elif asset.endswith('.jar'):
                resource_types['Java库'] += 1
            elif asset.endswith('.so'):
                resource_types['原生库'] += 1
        
        print("🎮 游戏场景分析:")
        for scene, count in sorted(boss_scenes.items()):
            print(f"  {scene}: {count} 个资源文件")
        
        print(f"\n📊 资源类型统计:")
        for res_type, count in sorted(resource_types.items(), key=lambda x: x[1], reverse=True):
            print(f"  {res_type}: {count:,} 个")
    
    def analyze_sdk_integration(self, data):
        """分析第三方SDK集成"""
        print("\n🔌 第三方SDK模块分析")
        print("=" * 50)
        
        assets = data.get("resources", {}).get("assets", [])
        libs = []
        
        # 从原生库获取SDK信息
        lib_info = data.get("native_libraries", {})
        for arch_libs in lib_info.get("libraries", {}).values():
            libs.extend(arch_libs)
        
        # SDK识别规则
        sdk_patterns = {
            "阿里云": ["ali", "alibaba"],
            "腾讯": ["tencent", "qq"],
            "字节跳动": ["pangle", "bytedance"],
            "友盟": ["umeng"],
            "TapTap": ["tap"],
            "快手": ["ks", "kuaishou"],
            "广点通": ["gdt"],
            "穿山甲": ["pangle"],
            "魅族": ["meizu"],
            "小米": ["xiaomi", "mi"],
            "华为": ["huawei", "hw"],
            "Cocos": ["cocos"],
            "MMKV": ["mmkv"],
            "Zeus": ["zeus"]
        }
        
        detected_sdks = defaultdict(list)
        
        # 分析原生库
        for lib in libs:
            lib_lower = lib.lower()
            for sdk_name, patterns in sdk_patterns.items():
                if any(pattern in lib_lower for pattern in patterns):
                    detected_sdks[sdk_name].append(lib)
        
        # 分析资源文件
        for asset in assets:
            asset_lower = asset.lower()
            for sdk_name, patterns in sdk_patterns.items():
                if any(pattern in asset_lower for pattern in patterns):
                    if asset not in detected_sdks[sdk_name]:
                        detected_sdks[sdk_name].append(asset)
        
        print("🏢 检测到的第三方SDK:")
        for sdk_name, components in detected_sdks.items():
            print(f"\n  {sdk_name}:")
            for component in components[:5]:  # 只显示前5个
                print(f"    - {component}")
            if len(components) > 5:
                print(f"    ... 还有 {len(components) - 5} 个组件")
    
    def analyze_game_features(self, data):
        """分析游戏功能特性"""
        print("\n🎯 游戏功能模块分析")
        print("=" * 50)
        
        assets = data.get("resources", {}).get("assets", [])
        
        # 功能模块识别
        features = {
            "Boss战斗系统": 0,
            "资源管理系统": 0,
            "配置系统": 0,
            "脚本系统": 0,
            "网络通信": 0,
            "数据存储": 0,
            "广告系统": 0,
            "支付系统": 0,
            "社交系统": 0,
            "安全系统": 0
        }
        
        # 分析资源文件
        for asset in assets:
            asset_lower = asset.lower()
            
            if 'boss' in asset_lower:
                features["Boss战斗系统"] += 1
            if 'config' in asset_lower:
                features["配置系统"] += 1
            if 'resources' in asset_lower:
                features["资源管理系统"] += 1
            if asset.endswith('.js') or asset.endswith('.jsc'):
                features["脚本系统"] += 1
            if 'network' in asset_lower or 'net' in asset_lower:
                features["网络通信"] += 1
            if 'data' in asset_lower or 'save' in asset_lower:
                features["数据存储"] += 1
            if 'ad' in asset_lower or 'ads' in asset_lower:
                features["广告系统"] += 1
            if 'pay' in asset_lower or 'purchase' in asset_lower:
                features["支付系统"] += 1
            if 'social' in asset_lower or 'friend' in asset_lower:
                features["社交系统"] += 1
            if 'security' in asset_lower or 'encrypt' in asset_lower:
                features["安全系统"] += 1
        
        print("🎮 游戏功能模块:")
        for feature, count in sorted(features.items(), key=lambda x: x[1], reverse=True):
            if count > 0:
                print(f"  {feature}: {count} 个相关文件")
    
    def analyze_file_structure(self, data):
        """分析文件结构模式"""
        print("\n📂 文件结构模式分析")
        print("=" * 50)
        
        structure = data.get("file_structure", {})
        directories = structure.get("directories", [])
        
        # 分析目录结构
        dir_categories = {
            "游戏资源": ["assets/assets"],
            "Android资源": ["res/"],
            "原生库": ["lib/"],
            "元数据": ["META-INF/"],
            "Kotlin": ["kotlin/"],
            "第三方库": ["okhttp3/", "org/apache/"]
        }
        
        categorized_dirs = defaultdict(list)
        
        for directory in directories:
            categorized = False
            for category, patterns in dir_categories.items():
                if any(pattern in directory for pattern in patterns):
                    categorized_dirs[category].append(directory)
                    categorized = True
                    break
            if not categorized:
                categorized_dirs["其他"].append(directory)
        
        print("📁 目录结构分类:")
        for category, dirs in categorized_dirs.items():
            print(f"\n  {category} ({len(dirs)} 个目录):")
            for directory in dirs[:5]:  # 只显示前5个
                print(f"    - {directory}")
            if len(dirs) > 5:
                print(f"    ... 还有 {len(dirs) - 5} 个目录")
    
    def generate_module_summary(self, data):
        """生成模块功能总结"""
        print("\n📋 模块功能总结")
        print("=" * 50)
        
        summary = {
            "应用类型": "大型手机游戏",
            "游戏引擎": "Cocos2d-js",
            "开发语言": "JavaScript + 原生代码",
            "主要功能": [
                "Boss战斗系统 - 多个Boss关卡",
                "资源管理系统 - 大量游戏资源",
                "配置系统 - JSON配置文件",
                "脚本系统 - JavaScript游戏逻辑",
                "网络通信 - 在线功能",
                "广告系统 - 多个广告SDK",
                "安全系统 - 多重安全保护",
                "数据统计 - 用户行为分析"
            ],
            "第三方集成": [
                "阿里云 - 认证服务",
                "腾讯 - 社交功能",
                "字节跳动 - 广告服务",
                "友盟 - 数据统计",
                "TapTap - 游戏平台",
                "Cocos - 游戏引擎"
            ],
            "技术特点": [
                "多架构支持 (arm64-v8a, armeabi-v7a)",
                "大量JSON配置文件 (10,000+)",
                "丰富的图片资源 (2,000+)",
                "模块化设计",
                "安全加固"
            ]
        }
        
        print(f"🎮 应用类型: {summary['应用类型']}")
        print(f"⚙️ 游戏引擎: {summary['游戏引擎']}")
        print(f"💻 开发语言: {summary['开发语言']}")
        
        print(f"\n🔧 主要功能模块:")
        for feature in summary["主要功能"]:
            print(f"  • {feature}")
        
        print(f"\n🔌 第三方服务集成:")
        for integration in summary["第三方集成"]:
            print(f"  • {integration}")
        
        print(f"\n⭐ 技术特点:")
        for feature in summary["技术特点"]:
            print(f"  • {feature}")
    
    def run(self):
        """运行模块分析"""
        print("🔍 APK模块功能深度分析")
        print("=" * 60)
        
        data = self.load_analysis_data()
        if not data:
            return
        
        # 执行各项分析
        self.analyze_native_libraries(data)
        self.analyze_assets_structure(data)
        self.analyze_sdk_integration(data)
        self.analyze_game_features(data)
        self.analyze_file_structure(data)
        self.generate_module_summary(data)
        
        print("\n" + "=" * 60)
        print("✅ 模块功能分析完成！")

def main():
    analyzer = ModuleAnalyzer()
    analyzer.run()

if __name__ == "__main__":
    main()
