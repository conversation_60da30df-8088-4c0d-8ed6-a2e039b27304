#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK分析脚本
提供APK文件的详细分析功能
"""

import os
import sys
import json
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path
import hashlib

class APKAnalyzer:
    def __init__(self):
        self.apk_dir = Path("apk")
        self.output_dir = Path("output")
        self.analysis_dir = self.output_dir / "analysis"
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
    
    def calculate_hash(self, file_path):
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        hash_sha256 = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
                hash_sha256.update(chunk)
        
        return {
            "md5": hash_md5.hexdigest(),
            "sha256": hash_sha256.hexdigest()
        }
    
    def get_apk_info(self, apk_path):
        """获取APK基本信息"""
        info = {
            "file_name": apk_path.name,
            "file_size": apk_path.stat().st_size,
            "file_path": str(apk_path)
        }
        
        # 计算哈希值
        info.update(self.calculate_hash(apk_path))
        
        return info
    
    def extract_manifest(self, apk_path):
        """提取AndroidManifest.xml信息"""
        manifest_info = {}
        
        try:
            with zipfile.ZipFile(apk_path, 'r') as zip_ref:
                # 检查是否存在AndroidManifest.xml
                if 'AndroidManifest.xml' in zip_ref.namelist():
                    manifest_data = zip_ref.read('AndroidManifest.xml')
                    
                    # 保存原始manifest文件
                    manifest_file = self.analysis_dir / f"{apk_path.stem}_manifest.xml"
                    with open(manifest_file, 'wb') as f:
                        f.write(manifest_data)
                    
                    manifest_info["manifest_file"] = str(manifest_file)
                    manifest_info["manifest_size"] = len(manifest_data)
                    
                    # 注意：这里的manifest是二进制格式，需要apktool解析
                    manifest_info["note"] = "需要使用apktool解析二进制manifest文件"
        
        except Exception as e:
            manifest_info["error"] = str(e)
        
        return manifest_info
    
    def analyze_dex_files(self, apk_path):
        """分析DEX文件"""
        dex_info = {
            "dex_files": [],
            "total_dex_size": 0
        }
        
        try:
            with zipfile.ZipFile(apk_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.endswith('.dex'):
                        dex_data = zip_ref.read(file_name)
                        dex_info["dex_files"].append({
                            "name": file_name,
                            "size": len(dex_data)
                        })
                        dex_info["total_dex_size"] += len(dex_data)
        
        except Exception as e:
            dex_info["error"] = str(e)
        
        return dex_info
    
    def analyze_resources(self, apk_path):
        """分析资源文件"""
        resources_info = {
            "assets": [],
            "res": [],
            "lib": [],
            "other": []
        }
        
        try:
            with zipfile.ZipFile(apk_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.startswith('assets/'):
                        resources_info["assets"].append(file_name)
                    elif file_name.startswith('res/'):
                        resources_info["res"].append(file_name)
                    elif file_name.startswith('lib/'):
                        resources_info["lib"].append(file_name)
                    elif not file_name.endswith('.dex') and file_name != 'AndroidManifest.xml':
                        resources_info["other"].append(file_name)
        
        except Exception as e:
            resources_info["error"] = str(e)
        
        return resources_info
    
    def analyze_native_libraries(self, apk_path):
        """分析原生库文件"""
        lib_info = {
            "architectures": [],
            "libraries": {}
        }
        
        try:
            with zipfile.ZipFile(apk_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    if file_name.startswith('lib/') and file_name.endswith('.so'):
                        parts = file_name.split('/')
                        if len(parts) >= 3:
                            arch = parts[1]
                            lib_name = parts[2]
                            
                            if arch not in lib_info["architectures"]:
                                lib_info["architectures"].append(arch)
                            
                            if arch not in lib_info["libraries"]:
                                lib_info["libraries"][arch] = []
                            
                            lib_info["libraries"][arch].append(lib_name)
        
        except Exception as e:
            lib_info["error"] = str(e)
        
        return lib_info
    
    def get_file_structure(self, apk_path):
        """获取APK文件结构"""
        structure = {
            "total_files": 0,
            "directories": set(),
            "file_types": {}
        }
        
        try:
            with zipfile.ZipFile(apk_path, 'r') as zip_ref:
                for file_name in zip_ref.namelist():
                    structure["total_files"] += 1
                    
                    # 添加目录
                    dir_path = '/'.join(file_name.split('/')[:-1])
                    if dir_path:
                        structure["directories"].add(dir_path)
                    
                    # 统计文件类型
                    if '.' in file_name:
                        ext = file_name.split('.')[-1].lower()
                        structure["file_types"][ext] = structure["file_types"].get(ext, 0) + 1
        
        except Exception as e:
            structure["error"] = str(e)
        
        # 转换set为list以便JSON序列化
        structure["directories"] = sorted(list(structure["directories"]))
        
        return structure
    
    def analyze_apk(self, apk_path):
        """完整分析APK文件"""
        print(f"\n开始分析APK: {apk_path.name}")
        
        analysis_result = {
            "apk_info": self.get_apk_info(apk_path),
            "manifest": self.extract_manifest(apk_path),
            "dex_files": self.analyze_dex_files(apk_path),
            "resources": self.analyze_resources(apk_path),
            "native_libraries": self.analyze_native_libraries(apk_path),
            "file_structure": self.get_file_structure(apk_path)
        }
        
        # 保存分析结果
        result_file = self.analysis_dir / f"{apk_path.stem}_analysis.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False)
        
        print(f"分析完成，结果保存到: {result_file}")
        
        # 打印摘要
        self.print_summary(analysis_result)
        
        return analysis_result
    
    def print_summary(self, analysis_result):
        """打印分析摘要"""
        print("\n=== APK分析摘要 ===")
        
        apk_info = analysis_result["apk_info"]
        print(f"文件名: {apk_info['file_name']}")
        print(f"文件大小: {apk_info['file_size']:,} bytes")
        print(f"MD5: {apk_info['md5']}")
        print(f"SHA256: {apk_info['sha256']}")
        
        dex_info = analysis_result["dex_files"]
        print(f"\nDEX文件: {len(dex_info['dex_files'])} 个")
        print(f"DEX总大小: {dex_info['total_dex_size']:,} bytes")
        
        lib_info = analysis_result["native_libraries"]
        if lib_info["architectures"]:
            print(f"\n支持架构: {', '.join(lib_info['architectures'])}")
        
        structure = analysis_result["file_structure"]
        print(f"\n文件总数: {structure['total_files']}")
        print(f"目录数: {len(structure['directories'])}")
        
        if structure["file_types"]:
            print("\n文件类型分布:")
            for ext, count in sorted(structure["file_types"].items()):
                print(f"  .{ext}: {count} 个")
    
    def run(self, apk_file=None):
        """运行分析流程"""
        print("APK分析工具启动")
        
        if apk_file:
            apk_files = [Path(apk_file)]
        else:
            apk_files = list(self.apk_dir.glob("*.apk"))
        
        if not apk_files:
            print("没有找到要分析的APK文件")
            return
        
        for apk_path in apk_files:
            try:
                self.analyze_apk(apk_path)
            except Exception as e:
                print(f"分析 {apk_path.name} 时出错: {e}")
        
        print("\n所有APK文件分析完成")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="APK分析工具")
    parser.add_argument("--apk", help="指定要分析的APK文件")
    
    args = parser.parse_args()
    
    analyzer = APKAnalyzer()
    analyzer.run(args.apk)

if __name__ == "__main__":
    main()
