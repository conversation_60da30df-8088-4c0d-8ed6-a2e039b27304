@echo off
chcp 65001 >nul
echo APK反编译工具
echo ================

echo 1. 下载工具
echo 2. 分析APK
echo 3. 反编译APK
echo 4. 退出

set /p choice="请选择操作 (1-4): "

if "%choice%"=="1" (
    echo 正在下载反编译工具...
    python scripts/download_tools.py
    pause
    goto :start
)

if "%choice%"=="2" (
    echo 正在分析APK文件...
    python scripts/analyze_apk.py
    pause
    goto :start
)

if "%choice%"=="3" (
    echo 正在反编译APK文件...
    python scripts/decompile.py
    pause
    goto :start
)

if "%choice%"=="4" (
    echo 退出程序
    exit /b 0
)

echo 无效选择，请重新输入
pause

:start
cls
goto :eof
