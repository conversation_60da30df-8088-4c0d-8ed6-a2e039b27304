# 使用说明

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.7 或更高版本
- Java 8 或更高版本

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 下载工具

运行以下命令下载反编译工具：

```bash
python scripts/download_tools.py
```

或者使用批处理文件：

```bash
run.bat
```

选择选项 1 下载工具。

### 4. 放置APK文件

将要反编译的APK文件放入 `apk/` 目录中。

### 5. 开始反编译

#### 方法一：使用批处理文件
```bash
run.bat
```

#### 方法二：直接运行Python脚本

分析APK：
```bash
python scripts/analyze_apk.py
```

反编译APK：
```bash
python scripts/decompile.py
```

## 详细功能

### APK分析

APK分析功能会提供以下信息：
- 文件基本信息（大小、哈希值等）
- DEX文件分析
- 资源文件统计
- 原生库支持的架构
- 文件结构概览

### APK反编译

支持三种反编译方式：

1. **apktool** - 反编译为smali代码和资源文件
2. **jadx** - 反编译为Java源码
3. **dex2jar** - 转换为JAR文件

### 输出结构

反编译完成后，输出文件将保存在 `output/` 目录中：

```
output/
├── [APK名称]/
│   ├── apktool/          # apktool输出
│   ├── jadx/             # jadx输出
│   ├── dex2jar/          # dex2jar输出
│   └── decompile_report.json  # 反编译报告
└── analysis/
    └── [APK名称]_analysis.json  # 分析报告
```

## 配置选项

编辑 `config/config.json` 文件可以自定义：

- 工具路径
- 输出格式
- 分析选项
- 过滤规则

## 常见问题

### Q: 工具下载失败怎么办？
A: 可以手动下载工具并放置到相应目录：
- apktool.jar → tools/apktool.jar
- jadx → tools/jadx/
- dex2jar → tools/dex2jar/

### Q: 反编译失败怎么办？
A: 检查：
1. Java是否正确安装
2. APK文件是否完整
3. 工具是否正确下载

### Q: 如何反编译特定的APK文件？
A: 使用命令行参数：
```bash
python scripts/decompile.py --apk path/to/your.apk
```

## 工具版本

- apktool: 2.8.1
- jadx: 1.4.7
- dex2jar: 2.4
