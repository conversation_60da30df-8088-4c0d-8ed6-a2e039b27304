#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK内容提取脚本
直接提取APK中的文件内容
"""

import zipfile
import os
from pathlib import Path

def extract_apk_contents():
    """提取APK文件内容"""
    apk_path = Path("apk/com.legend.startknife.tap_75.apk")
    output_dir = Path("output/extracted_apk")
    
    if not apk_path.exists():
        print(f"APK文件不存在: {apk_path}")
        return
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"正在提取APK内容到: {output_dir}")
    
    try:
        with zipfile.ZipFile(apk_path, 'r') as zip_ref:
            # 提取所有文件
            zip_ref.extractall(output_dir)
            
            # 列出主要文件
            print("\n📁 APK文件结构:")
            for file_info in zip_ref.filelist[:20]:  # 只显示前20个文件
                print(f"  {file_info.filename}")
            
            if len(zip_ref.filelist) > 20:
                print(f"  ... 还有 {len(zip_ref.filelist) - 20} 个文件")
            
            print(f"\n✅ 提取完成，共 {len(zip_ref.filelist)} 个文件")
            
            # 查找重要文件
            important_files = []
            for file_info in zip_ref.filelist:
                filename = file_info.filename.lower()
                if any(keyword in filename for keyword in ['classes.dex', 'androidmanifest.xml', 'main.js', 'config.json']):
                    important_files.append(file_info.filename)
            
            if important_files:
                print(f"\n🔍 重要文件:")
                for file in important_files:
                    print(f"  {file}")
            
    except Exception as e:
        print(f"提取失败: {e}")

def list_dex_files():
    """列出DEX文件"""
    extracted_dir = Path("output/extracted_apk")
    
    if not extracted_dir.exists():
        print("请先运行提取功能")
        return
    
    dex_files = list(extracted_dir.glob("*.dex"))
    
    if dex_files:
        print(f"\n📦 找到 {len(dex_files)} 个DEX文件:")
        for dex_file in dex_files:
            size = dex_file.stat().st_size
            print(f"  {dex_file.name}: {size:,} bytes")
    else:
        print("未找到DEX文件")

def list_js_files():
    """列出JavaScript文件"""
    extracted_dir = Path("output/extracted_apk")
    
    if not extracted_dir.exists():
        print("请先运行提取功能")
        return
    
    js_files = []
    for root, dirs, files in os.walk(extracted_dir):
        for file in files:
            if file.endswith(('.js', '.jsc')):
                js_files.append(os.path.join(root, file))
    
    if js_files:
        print(f"\n📜 找到 {len(js_files)} 个JavaScript文件:")
        for js_file in js_files[:10]:  # 只显示前10个
            rel_path = os.path.relpath(js_file, extracted_dir)
            print(f"  {rel_path}")
        
        if len(js_files) > 10:
            print(f"  ... 还有 {len(js_files) - 10} 个文件")
    else:
        print("未找到JavaScript文件")

def show_manifest():
    """显示AndroidManifest.xml内容"""
    manifest_path = Path("output/extracted_apk/AndroidManifest.xml")
    
    if manifest_path.exists():
        print(f"\n📋 AndroidManifest.xml (前50行):")
        try:
            with open(manifest_path, 'rb') as f:
                content = f.read()
                print(f"文件大小: {len(content)} bytes")
                print("注意: 这是二进制格式的manifest文件，需要apktool解析")
        except Exception as e:
            print(f"读取manifest失败: {e}")
    else:
        print("未找到AndroidManifest.xml")

def main():
    """主函数"""
    print("🔍 APK内容提取工具")
    print("=" * 50)
    
    # 提取APK内容
    extract_apk_contents()
    
    # 列出重要文件类型
    list_dex_files()
    list_js_files()
    show_manifest()
    
    print("\n" + "=" * 50)
    print("✅ APK内容提取完成!")
    print(f"📁 提取的文件位于: output/extracted_apk/")

if __name__ == "__main__":
    main()
