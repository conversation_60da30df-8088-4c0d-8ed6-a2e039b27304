#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java环境安装脚本
自动下载并安装OpenJDK
"""

import os
import sys
import requests
import zipfile
import subprocess
from pathlib import Path
import shutil

class JavaInstaller:
    def __init__(self):
        self.java_dir = Path("java")
        self.java_dir.mkdir(exist_ok=True)
        
        # OpenJDK下载链接 (Microsoft Build of OpenJDK 17)
        self.jdk_url = "https://aka.ms/download-jdk/microsoft-jdk-17.0.12-windows-x64.zip"
        self.jdk_filename = "microsoft-jdk-17.0.12-windows-x64.zip"
        
    def check_existing_java(self):
        """检查是否已有Java环境"""
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 系统已安装Java:")
                print(result.stderr.split('\n')[0])
                return True
        except FileNotFoundError:
            pass
        
        # 检查本地Java
        local_java = self.java_dir / "bin" / "java.exe"
        if local_java.exists():
            print("✅ 找到本地Java安装")
            return True
        
        return False
    
    def download_jdk(self):
        """下载OpenJDK"""
        jdk_path = self.java_dir / self.jdk_filename
        
        if jdk_path.exists():
            print(f"✅ JDK文件已存在: {jdk_path}")
            return jdk_path
        
        print(f"📥 正在下载OpenJDK...")
        print(f"下载地址: {self.jdk_url}")
        
        try:
            response = requests.get(self.jdk_url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(jdk_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            print(f"\r下载进度: {percent:.1f}%", end='', flush=True)
            
            print(f"\n✅ 下载完成: {jdk_path}")
            return jdk_path
        
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            return None
    
    def extract_jdk(self, jdk_path):
        """解压JDK"""
        print(f"📦 正在解压JDK...")
        
        try:
            with zipfile.ZipFile(jdk_path, 'r') as zip_ref:
                zip_ref.extractall(self.java_dir)
            
            # 查找解压后的JDK目录
            jdk_dirs = [d for d in self.java_dir.iterdir() 
                       if d.is_dir() and 'jdk' in d.name.lower()]
            
            if jdk_dirs:
                jdk_extracted = jdk_dirs[0]
                jdk_final = self.java_dir / "jdk"
                
                # 重命名为标准目录名
                if jdk_extracted != jdk_final:
                    if jdk_final.exists():
                        try:
                            shutil.rmtree(jdk_final)
                        except:
                            pass
                    try:
                        jdk_extracted.rename(jdk_final)
                    except:
                        # 如果重命名失败，直接使用原目录
                        jdk_final = jdk_extracted
                
                print(f"✅ JDK解压完成: {jdk_final}")
                
                # 删除zip文件
                jdk_path.unlink()
                
                return jdk_final
            else:
                print("❌ 未找到解压后的JDK目录")
                return None
        
        except Exception as e:
            print(f"❌ 解压失败: {e}")
            return None
    
    def setup_java_path(self, jdk_path):
        """设置Java路径"""
        java_exe = jdk_path / "bin" / "java.exe"
        javac_exe = jdk_path / "bin" / "javac.exe"
        
        if java_exe.exists() and javac_exe.exists():
            print(f"✅ Java可执行文件: {java_exe}")
            
            # 测试Java版本
            try:
                result = subprocess.run([str(java_exe), '-version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ Java版本信息:")
                    print(result.stderr.split('\n')[0])
                    return str(java_exe)
            except Exception as e:
                print(f"❌ Java测试失败: {e}")
        
        return None
    
    def update_config(self, java_path):
        """更新配置文件中的Java路径"""
        config_file = Path("config/config.json")
        
        if config_file.exists():
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 更新Java路径
                config["java_path"] = java_path
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                
                print(f"✅ 已更新配置文件: {config_file}")
                
            except Exception as e:
                print(f"❌ 更新配置失败: {e}")
    
    def create_java_env_script(self, java_path):
        """创建Java环境脚本"""
        java_dir = Path(java_path).parent.parent
        
        # 创建环境变量脚本
        env_script = Path("set_java_env.bat")
        with open(env_script, 'w') as f:
            f.write(f'@echo off\n')
            f.write(f'set JAVA_HOME={java_dir}\n')
            f.write(f'set PATH=%JAVA_HOME%\\bin;%PATH%\n')
            f.write(f'echo Java环境已设置\n')
            f.write(f'echo JAVA_HOME=%JAVA_HOME%\n')
            f.write(f'java -version\n')
        
        print(f"✅ 创建环境脚本: {env_script}")
        
        # 创建反编译脚本
        decompile_script = Path("decompile_with_java.bat")
        with open(decompile_script, 'w') as f:
            f.write(f'@echo off\n')
            f.write(f'set JAVA_HOME={java_dir}\n')
            f.write(f'set PATH=%JAVA_HOME%\\bin;%PATH%\n')
            f.write(f'echo 开始反编译APK...\n')
            f.write(f'python scripts/decompile.py\n')
            f.write(f'pause\n')
        
        print(f"✅ 创建反编译脚本: {decompile_script}")
    
    def install(self):
        """执行安装流程"""
        print("🔧 Java环境安装程序")
        print("=" * 50)
        
        # 检查现有Java
        if self.check_existing_java():
            print("Java环境已可用，无需重新安装")
            return True
        
        print("📥 开始安装Java环境...")
        
        # 下载JDK
        jdk_path = self.download_jdk()
        if not jdk_path:
            return False
        
        # 解压JDK
        jdk_dir = self.extract_jdk(jdk_path)
        if not jdk_dir:
            return False
        
        # 设置Java路径
        java_exe = self.setup_java_path(jdk_dir)
        if not java_exe:
            return False
        
        # 更新配置
        self.update_config(java_exe)
        
        # 创建环境脚本
        self.create_java_env_script(java_exe)
        
        print("\n" + "=" * 50)
        print("✅ Java环境安装完成!")
        print(f"📁 安装路径: {jdk_dir}")
        print(f"☕ Java可执行文件: {java_exe}")
        print("\n💡 使用方法:")
        print("1. 运行 decompile_with_java.bat 进行反编译")
        print("2. 或者运行 set_java_env.bat 设置环境变量")
        
        return True

def main():
    installer = JavaInstaller()
    success = installer.install()
    
    if success:
        print("\n🚀 现在可以运行反编译工具了!")
    else:
        print("\n❌ Java安装失败，请手动安装Java环境")

if __name__ == "__main__":
    main()
