# 反编译代码路径指南

## 📁 **主要反编译输出路径**

### 🎯 **当前状态**
- **完整APK提取**: ✅ 成功
- **Java反编译**: ❌ 需要Java环境
- **Smali反编译**: ❌ 需要Java环境

---

## 📂 **反编译代码的具体路径**

### 1. **APK内容提取路径**
```
output/extracted_apk/
├── 📱 Android核心文件
│   ├── AndroidManifest.xml          # Android清单文件(二进制)
│   ├── classes.dex                  # 主要DEX文件(13.7MB)
│   └── resources.arsc               # 编译后的资源文件
│
├── 🎮 游戏代码和资源
│   ├── assets/
│   │   ├── main.js                  # 游戏主入口脚本
│   │   ├── project.json             # Cocos2d项目配置
│   │   ├── src/                     # 游戏源码目录
│   │   │   ├── cocos2d-jsb.jsc     # Cocos引擎绑定(编译后)
│   │   │   ├── physics.jsc         # 物理引擎(编译后)
│   │   │   └── settings.jsc        # 游戏设置(编译后)
│   │   └── assets/                  # 游戏资源
│   │       ├── Boss1/config.json   # Boss1配置
│   │       ├── Boss2/config.json   # Boss2配置
│   │       ├── Boss3/config.json   # Boss3配置
│   │       ├── Boss4/config.json   # Boss4配置
│   │       ├── Boss5/config.json   # Boss5配置
│   │       └── Boss6/config.json   # Boss6配置
│
├── 🔧 原生库文件
│   └── lib/
│       ├── arm64-v8a/              # 64位ARM库
│       │   ├── libthemis.so        # 加密库
│       │   ├── libcocos2djs.so     # 游戏引擎
│       │   ├── libmmkv.so          # 存储库
│       │   └── [其他12个.so文件]
│       └── armeabi-v7a/            # 32位ARM库
│           └── [相同的.so文件]
│
└── 📱 Android资源
    └── res/                        # Android UI资源
        ├── layout/                 # 布局文件
        ├── drawable/               # 图片资源
        └── xml/                    # XML配置
```

### 2. **预期的反编译路径** (需要Java环境)
```
output/com.legend.startknife.tap_75/
├── apktool/                        # apktool输出
│   ├── AndroidManifest.xml         # 解析后的清单文件
│   ├── smali/                      # Smali汇编代码
│   │   ├── com/                    # 应用包结构
│   │   ├── androidx/               # Android支持库
│   │   └── kotlin/                 # Kotlin标准库
│   └── res/                        # 解析后的资源文件
│
├── jadx/                           # jadx输出
│   ├── sources/                    # Java源码
│   │   ├── com/                    # 应用Java代码
│   │   ├── androidx/               # 支持库代码
│   │   └── kotlin/                 # Kotlin代码
│   └── resources/                  # 资源文件
│
└── dex2jar/                        # dex2jar输出
    └── com.legend.startknife.tap_75.jar  # 转换后的JAR文件
```

---

## 🔍 **关键代码文件位置**

### **JavaScript游戏逻辑**
- **主入口**: `output/extracted_apk/assets/main.js`
- **游戏配置**: `output/extracted_apk/assets/project.json`
- **编译后脚本**: `output/extracted_apk/assets/src/*.jsc`

### **游戏配置数据**
- **Boss配置**: `output/extracted_apk/assets/assets/Boss*/config.json`
- **数据表**: `output/extracted_apk/assets/assets/DataTable/config.json`
- **武器配置**: `output/extracted_apk/assets/assets/Weapon*/config.json`

### **原生库代码** (二进制文件)
- **加密库**: `output/extracted_apk/lib/*/libthemis.so`
- **游戏引擎**: `output/extracted_apk/lib/*/libcocos2djs.so`
- **存储库**: `output/extracted_apk/lib/*/libmmkv.so`
- **安全库**: `output/extracted_apk/lib/*/libsecuritydevice.so`

### **Android应用代码** (需要反编译)
- **DEX文件**: `output/extracted_apk/classes.dex` (13.7MB)
- **清单文件**: `output/extracted_apk/AndroidManifest.xml` (二进制)

---

## 🛠️ **如何获取完整的反编译代码**

### **方法1: 安装Java环境**
```bash
# 1. 安装Java 8+
# 2. 运行反编译脚本
python scripts/decompile.py
```

### **方法2: 手动使用工具**
```bash
# 使用apktool (需要Java)
java -jar tools/apktool.jar d apk/com.legend.startknife.tap_75.apk -o output/apktool_output

# 使用jadx (需要Java)
tools/jadx/bin/jadx.bat -d output/jadx_output apk/com.legend.startknife.tap_75.apk
```

### **方法3: 在线反编译工具**
- 上传APK到在线反编译平台
- 下载反编译结果

---

## 📊 **当前可用的代码分析**

### ✅ **已可访问的内容**
1. **JavaScript游戏逻辑** - 可直接查看
2. **游戏配置文件** - JSON格式，可直接分析
3. **资源文件结构** - 完整的游戏资源
4. **原生库列表** - 了解使用的技术栈

### ❌ **需要进一步反编译的内容**
1. **Android Java代码** - 在classes.dex中
2. **网络通信逻辑** - 可能在Java代码中
3. **加密实现细节** - 在原生库中
4. **cipherTxt生成逻辑** - 可能在Java或原生代码中

---

## 🎯 **寻找cipherTxt相关代码的建议路径**

### **1. JavaScript层面**
- 查看: `output/extracted_apk/assets/main.js`
- 搜索关键词: `encrypt`, `cipher`, `request`, `network`

### **2. 配置文件层面**
- 查看: `output/extracted_apk/assets/assets/*/config.json`
- 寻找网络配置和加密参数

### **3. 原生库层面** (需要专业工具)
- 分析: `libthemis.so` (加密库)
- 分析: `libalicomphonenumberauthsdk_core.so` (认证SDK)

### **4. Java代码层面** (需要反编译DEX)
- 反编译: `classes.dex`
- 搜索网络请求和加密相关的类

---

## 💡 **下一步建议**

1. **安装Java环境**，完成完整反编译
2. **分析JavaScript代码**，查找网络请求逻辑
3. **研究配置文件**，了解加密参数
4. **使用专业工具**分析原生库

当前您可以直接查看和分析JavaScript代码和配置文件，这些可能包含cipherTxt生成的相关逻辑！
